import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { First } from "./first/first";
import { Box } from "./box/box";
import { VerticalBoxes } from "./vertical-boxes/vertical-boxes";
import { HorizontalBoxes } from "./horizontal-boxes/horizontal-boxes";

@Component({
  selector: 'app-root',
  // imports: [RouterOutlet],
  templateUrl: './app.html',
  styleUrl: './app.css',
  imports: [First, Box, VerticalBoxes, HorizontalBoxes]
})
export class App {
  protected readonly title = signal('Chess');

  numberOfReps: number[] = [1,2,3,4,5,6,7,8]; 
}