
.container{
    padding-left: 0;
    padding-top: 1rem;
    width: 18rem;
    display: inline-block;
    background-color: white;
    border-radius: 2rem;

}
.img{
    height: 11rem;
    width: 17rem;
    border-radius: 0.55rem;

}
.profile-details{
    width: 15rem;
}
.img-container{
margin-bottom: 0.5rem;
padding-right: 1rem;
margin-right: 0;

}
.profile-img{
height: 3rem;
width: 3rem;
border-radius: 1.5rem;
}
.profile-img-container{
    display: flex;
    width: 18rem;
    margin-bottom: 0.5rem;

}
.video-title{
    font-size: 14px;
    margin-left: 1rem;
}

.video-description{
    
    width: 100%;
    color:gray;
}
.video-description-1{
    margin-top: 0;
    margin-bottom: 0px;
    padding-left: 5rem;
    padding-right: 0rem;
    
}
.video-description-2{
    margin-top: 0.5rem;
    padding-left: 5rem;
}
.first-cont{
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex-wrap: wrap-reverse;
}


.left-section{
    display: flex;
    align-items: center;
}
.middle-section{
    flex: 1;
    display: flex;
    align-items: center;
    margin-left: 70px;
    max-width: 500px;
margin-right: 35px;
}
.right-section{
    width: 180px;
    display: flex;
    align-items: center;
    justify-content: space-between;  
    flex-shrink: 0;
}
.hamburger-menu{
    height: 20px;
    margin-left: 24px;
    margin-right: 20px;
}
.youtube-logo{
    height: 20px;
}

.search-bar::placeholder{
    font-family: 'Times New Roman', Times, serif;
    font-size: 16px ;
}
.upload-icon{
    height: 24px;
}

.first-row{
    margin-top: 3rem;
}
.main-body{
    margin-left: 5rem;
}
.container-box{
    display: grid;
    
}