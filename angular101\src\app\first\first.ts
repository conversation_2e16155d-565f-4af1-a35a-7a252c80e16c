import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';  

@Component({
  selector: 'app-first',
  imports: [CommonModule],
  templateUrl: './first.html',
  styleUrl: './first.css'
})
export class First {

  age = 5;
  firstname = "<PERSON>";
  money =3.6;
  Today : Date = new Date();
  isValid : boolean = true;
  isValue = null;
  UndefinedVariable: any;
  numbers: number[] = [1, 2, 3, 4, 5];









}
